# Drag Fix Test Plan

## Bug Description
When there are 2+ shapes on the board and you start dragging a shape without selecting it first, the shape gets selected correctly but does not move. Instead, the entire zoomable board gets panned as if performing a pan gesture.

## Root Cause
The issue was in the `TransformableShape._handleShapeDrag` method which had a guard condition:
```dart
if (!widget.selected) return;
```

When dragging an unselected shape:
1. `onPanStart` is called on the `TransformableShape`
2. The shape gets selected in `_onShapeDragStart`
3. But `_handleShapeDrag` immediately returns because `widget.selected` is still `false` at that moment
4. The drag gesture gets passed through to the `InteractiveViewer`, causing panning instead of shape dragging

## Fix Applied
Modified the guard condition in `_handleShapeDrag` to:
```dart
// Allow dragging if the shape is selected OR if we're currently dragging
// This fixes the issue where unselected shapes can't be dragged when multiple shapes exist
if (!widget.selected && !_isDragging) return;
```

The `_isDragging` flag is set to `true` in `_onShapeDragStart` before the first `_handleShapeDrag` call, allowing the drag to proceed even if the widget hasn't been rebuilt with the new selection state yet.

## Test Cases

### Test Case 1: Single Shape Scenario (Should continue working)
1. Create 1 shape on the board
2. Click and drag the shape without selecting it first
3. **Expected**: Shape should be selected and dragged immediately
4. **Status**: Should work as before (no regression)

### Test Case 2: Multiple Shapes Scenario (Bug fix)
1. Create 2+ shapes on the board
2. Click and drag an unselected shape
3. **Expected**: Shape should be selected and dragged immediately (no panning)
4. **Status**: Should now work correctly

### Test Case 3: Already Selected Shape (Should continue working)
1. Create 2+ shapes on the board
2. Select a shape first
3. Drag the selected shape
4. **Expected**: Shape should drag normally
5. **Status**: Should work as before (no regression)

### Test Case 4: Drag on Empty Area (Should continue working)
1. Create 2+ shapes on the board
2. Click and drag on empty area (not on any shape)
3. **Expected**: Board should pan normally
4. **Status**: Should work as before (no regression)

## Files Modified
- `lib/app/modules/shape_test/views/transformable_shape.dart`
  - Modified `_handleShapeDrag` method guard condition (line 637)

## Technical Details
- The `_isDragging` flag is properly managed:
  - Set to `true` in `_onShapeDragStart` (line 418)
  - Set to `false` in `_onDragEnd` (line 435) and `_handleDragEnd` (line 453)
- The fix maintains all existing functionality while allowing immediate dragging of unselected shapes
- No changes to the selection logic or other drag handling mechanisms
