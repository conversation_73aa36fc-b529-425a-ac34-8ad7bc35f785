import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:get/get.dart';
import '../constants/grid_constants.dart';
import '../controllers/shape_editor_controller.dart';

/// Widget that handles zoom and pan transformations for the board
/// using <PERSON><PERSON><PERSON>'s built-in InteractiveViewer for better performance and behavior
class ZoomableBoard extends StatefulWidget {
  final Widget child;
  final ShapeEditorController controller;

  const ZoomableBoard({
    super.key,
    required this.child,
    required this.controller,
  });

  @override
  State<ZoomableBoard> createState() => _ZoomableBoardState();
}

class _ZoomableBoardState extends State<ZoomableBoard> {
  late final TransformationController _transformationController;

  @override
  void initState() {
    super.initState();
    _transformationController = widget.controller.transformationController;

    // Set initial identity matrix
    _transformationController.value = Matrix4.identity();

    // Initial setup will be done in the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _syncWithController();
    });
  }

  /// Synchronize with the external controller values
  void _syncWithController() {
    final matrix = Matrix4.identity()
      ..translate(widget.controller.panOffset.value.dx,
          widget.controller.panOffset.value.dy)
      ..scale(widget.controller.zoomScale.value);

    _transformationController.value = matrix;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // When controller values change, update our transformation
      if (_transformationController.value.getMaxScaleOnAxis() !=
          widget.controller.zoomScale.value) {
        _syncWithController();
      }

      // Get the current drag state to conditionally enable/disable panning
      final bool shouldDisablePanning = widget.controller.isValidDrag;

      // Debug logging to track panning state
      if (shouldDisablePanning) {
        debugPrint('[ZoomableBoard] Disabling panning - shape drag detected');
      }

      return LayoutBuilder(builder: (context, constraints) {
        // Calculate extended boundaries for more natural zooming/panning
        final extendedMargin = EdgeInsets.symmetric(
          horizontal: 50,
          vertical: 50,
        );

        return InteractiveViewer(
          transformationController: _transformationController,
          minScale: 0.1,
          maxScale: 8.0,
          constrained: false, // Allow content to overflow the viewport
          scaleEnabled: true,
          // Conditionally disable panning when a shape is being dragged
          // This fixes the issue where shape dragging conflicts with board panning
          panEnabled: !shouldDisablePanning,
          clipBehavior: Clip.none, // Prevent clipping during transformations
          onInteractionStart: (ScaleStartDetails details) {
            widget.controller.isPanning = true;

            // Set grid interaction state to true when zooming/panning starts
            widget.controller.setGridInteracting(true);

            // When scale interaction starts with multiple pointers, it's a pinch gesture
            if (details.pointerCount > 1) {
              // This helps our GlobalTapHandler know this is a zoom gesture
              widget.controller.activePointerCount = details.pointerCount;
            }
          },
          onInteractionEnd: (ScaleEndDetails details) {
            widget.controller.isPanning = false;

            // Update the grid system with the final transformation values
            final matrix = _transformationController.value;
            final scale = matrix.getMaxScaleOnAxis();
            final translation = matrix.getTranslation();

            // Set grid interaction to false when interaction ends
            widget.controller.setGridInteracting(false);

            // Update viewport with precise scale and translation
            widget.controller.gridSystem.updateViewport(
              scale,
              Offset(translation.x, translation.y),
              updateNeedleMapping: true, // Force needle mapping update
            );
          },
          onInteractionUpdate: (ScaleUpdateDetails details) {
            // Update controller from the transformation matrix
            final matrix = _transformationController.value;
            final scale = matrix.getMaxScaleOnAxis();
            final translation = matrix.getTranslation();

            widget.controller.zoomScale.value = scale;
            widget.controller.panOffset.value =
                Offset(translation.x, translation.y);

            // Update grid system during interaction for real-time feedback
            widget.controller.gridSystem.updateViewport(
              scale,
              Offset(translation.x, translation.y),
              updateNeedleMapping:
                  false, // Skip full remapping during interaction for performance
            );

            // Keep grid interaction state active during updates
            widget.controller.setGridInteracting(true);

            // Force an update to ensure the grid repaints
            widget.controller.update();
          },
          // Use larger boundary margin to allow more freedom in panning
          boundaryMargin: extendedMargin,
          child: SizedBox(
            // Match width to screen but extend height to allow vertical scrolling
            width: constraints.maxWidth,
            height: GridConstants.getExtendedHeight(constraints.maxHeight),
            child: widget.child, // The child contains the grid and shapes
          ),
        );
      });
    });
  }
}
