import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/utils/geometry_utils.dart';
import 'package:xoxknit/app/modules/shape_test/views/transformable_shape.dart';
import 'package:get/get.dart';
import 'dart:math' as math; // Import math for calculations

// Define minimum sizes - using much smaller precise values
const double minHandleHitboxSize = 16.0; // Just enough to tap the handle itself
const double minVertexHandleHitboxSize = 20.0; // Slightly larger for vertices

// Helper to calculate scaled size with minimal impact from zoom
double _calculateScaledSize(double baseSize, double zoomScale, double minSize) {
  // At higher zoom levels, make hitboxes smaller on screen to match visible handle size
  if (zoomScale <= 1.0) return baseSize;
  return math.max(
      minSize, baseSize / zoomScale); // Direct division for precise scaling
}

class GlobalTapHandler extends StatelessWidget {
  final Widget child;
  final ShapeEditorController controller;

  const GlobalTapHandler({
    super.key,
    required this.child,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: FocusNode(skipTraversal: true, canRequestFocus: true),
      autofocus: true,
      onKeyEvent: (event) {
        // Track modifier key states for transformation operations
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.shift ||
              event.logicalKey == LogicalKeyboardKey.shiftLeft ||
              event.logicalKey == LogicalKeyboardKey.shiftRight) {
            controller.isShiftKeyDown.value = true;
          }
        } else if (event is KeyUpEvent) {
          if (event.logicalKey == LogicalKeyboardKey.shift ||
              event.logicalKey == LogicalKeyboardKey.shiftLeft ||
              event.logicalKey == LogicalKeyboardKey.shiftRight) {
            controller.isShiftKeyDown.value = false;
          }
        }

        // Handle keyboard shortcuts
        if (event is KeyDownEvent) {
          // Delete key
          if (event.logicalKey == LogicalKeyboardKey.delete ||
              event.logicalKey == LogicalKeyboardKey.backspace) {
            if (controller.selectedIndices.isNotEmpty) {
              controller.deleteSelectedShapes();
            }
          }
          // === UNDO/REDO ===
          // Ctrl+Z for undo
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyZ &&
              !HardwareKeyboard.instance.isShiftPressed) {
            controller.undo();
          }
          // Ctrl+Shift+Z or Ctrl+Y for redo
          else if (((HardwareKeyboard.instance.isControlPressed ||
                      HardwareKeyboard.instance.isMetaPressed) &&
                  HardwareKeyboard.instance.isShiftPressed &&
                  event.logicalKey == LogicalKeyboardKey.keyZ) ||
              ((HardwareKeyboard.instance.isControlPressed ||
                      HardwareKeyboard.instance.isMetaPressed) &&
                  event.logicalKey == LogicalKeyboardKey.keyY)) {
            controller.redo();
          }

          // === CLIPBOARD OPERATIONS ===
          // Ctrl+C for copy
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyC &&
              controller.selectedIndices.isNotEmpty) {
            controller.copySelectedShapes();
          }
          // Ctrl+X for cut
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyX &&
              controller.selectedIndices.isNotEmpty) {
            controller.cutSelectedShapes();
          }
          // Ctrl+V for paste
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyV &&
              controller.canPaste()) {
            controller.pasteShapes();
          }
          // Ctrl+A for select all (if shapes exist)
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyA &&
              controller.shapes.isNotEmpty) {
            // Select all shapes
            controller.selectedIndices.clear();
            for (int i = 0; i < controller.shapes.length; i++) {
              controller.selectedIndices.add(i);
            }
            controller.update();
          }

          // === GROUPING ===
          // Ctrl+G for group
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyG &&
              !HardwareKeyboard.instance.isShiftPressed) {
            if (controller.canGroupSelectedShapes()) {
              controller.groupSelectedShapes();
            }
          }
          // Ctrl+Shift+G for ungroup
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              HardwareKeyboard.instance.isShiftPressed &&
              event.logicalKey == LogicalKeyboardKey.keyG) {
            if (controller.canUngroupSelectedShape()) {
              controller.ungroupSelectedShape();
            }
          }

          // === DUPLICATION ===
          // Ctrl+D for duplicate
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyD &&
              controller.selectedIndices.isNotEmpty) {
            controller.duplicateSelectedShapes();
          }

          // === TRANSFORMATIONS ===
          // H for flip horizontally
          else if (event.logicalKey == LogicalKeyboardKey.keyH &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed &&
              controller.selectedIndices.isNotEmpty) {
            controller.flipSelectedShapeHorizontally();
          }
          // V for flip vertically
          else if (event.logicalKey == LogicalKeyboardKey.keyV &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed &&
              controller.selectedIndices.isNotEmpty) {
            controller.flipSelectedShapeVertically();
          }
          // R for rotate by fixed angle
          else if (event.logicalKey == LogicalKeyboardKey.keyR &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed &&
              controller.selectedIndices.isNotEmpty) {
            controller.rotateSelectedShapesByFixedAngle();
          }

          // === MODE TOGGLES ===
          // C for toggle curve mode (on selected shape)
          else if (event.logicalKey == LogicalKeyboardKey.keyC &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed &&
              controller.selectedIndices.isNotEmpty) {
            controller.toggleCurveMode(controller.selectedIndices.first);
          }
          // E for toggle vertex edit mode
          else if (event.logicalKey == LogicalKeyboardKey.keyE &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed) {
            controller.toggleVertexEditMode();
          }
          // M for toggle mirror mode
          else if (event.logicalKey == LogicalKeyboardKey.keyM &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed) {
            controller.toggleMirrorMode();
          }
          // S for toggle multi-selection mode
          else if (event.logicalKey == LogicalKeyboardKey.keyS &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed) {
            controller.toggleMultiSelectionMode();
          }
          // T for start custom shape creation
          else if (event.logicalKey == LogicalKeyboardKey.keyT &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed &&
              !controller.isCreatingCustomShape.value) {
            controller.startCustomShapeCreation();
          }

          // === VIEW CONTROLS ===
          // 0 (zero) for reset zoom and center content
          else if (event.logicalKey == LogicalKeyboardKey.digit0 &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed) {
            controller.resetZoomAndCenterContent();
          }
          // F for fit to screen / reset zoom
          else if (event.logicalKey == LogicalKeyboardKey.keyF &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed) {
            controller.resetZoom();
          }

          // === DISPLAY TOGGLES ===
          // P for toggle property display mode
          else if (event.logicalKey == LogicalKeyboardKey.keyP &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed) {
            controller.togglePropertyDisplayMode();
          }
          // G for toggle grid labels (without Ctrl)
          else if (event.logicalKey == LogicalKeyboardKey.keyG &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed &&
              !HardwareKeyboard.instance.isShiftPressed) {
            controller.toggleGridLabels();
          }
          // N for toggle needle labels
          else if (event.logicalKey == LogicalKeyboardKey.keyN &&
              !HardwareKeyboard.instance.isControlPressed &&
              !HardwareKeyboard.instance.isMetaPressed) {
            controller.toggleNeedleLabels();
          }

          // === NUDGING WITH ARROW KEYS ===
          // Arrow keys for nudging selected shapes
          else if (controller.selectedIndices.isNotEmpty &&
              [
                LogicalKeyboardKey.arrowUp,
                LogicalKeyboardKey.arrowDown,
                LogicalKeyboardKey.arrowLeft,
                LogicalKeyboardKey.arrowRight
              ].contains(event.logicalKey)) {
            _handleArrowKeyNudge(event.logicalKey);
          }

          // === CUSTOM SHAPE CREATION SHORTCUTS ===
          // Enter to finish custom shape creation
          else if (event.logicalKey == LogicalKeyboardKey.enter &&
              controller.isCreatingCustomShape.value &&
              controller.customShapeVertices.length >= 3) {
            controller.finishCustomShapeCreation();
          }
          // Escape to cancel custom shape creation OR exit modes
          else if (event.logicalKey == LogicalKeyboardKey.escape) {
            if (controller.isCreatingCustomShape.value) {
              controller.cancelCustomShapeCreation();
            } else if (controller.isVertexEditModeActive.value) {
              controller.toggleVertexEditMode(); // Exit vertex edit mode
            } else if (controller.isMultiSelectionMode) {
              controller.isMultiSelectionMode = false;
            } else if (controller.selectedIndices.isNotEmpty) {
              controller.deselectAllShapes();
            }
          }

          // === FILE OPERATIONS ===
          // Ctrl+S for save
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyS &&
              !HardwareKeyboard.instance.isShiftPressed) {
            controller.saveToWizardState();
          }
          // Ctrl+Shift+N for clear all shapes (New document)
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              HardwareKeyboard.instance.isShiftPressed &&
              event.logicalKey == LogicalKeyboardKey.keyN) {
            if (controller.shapes.isNotEmpty) {
              // Show confirmation dialog for destructive action
              Get.dialog(
                AlertDialog(
                  title: const Text('Clear All Shapes'),
                  content: const Text(
                      'Are you sure you want to clear all shapes? This action cannot be undone.'),
                  actions: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () {
                        Get.back();
                        controller.clearAllShapes();
                      },
                      child: const Text('Clear All'),
                    ),
                  ],
                ),
              );
            }
          }

          // === VERTEX EDIT MODE SHORTCUTS ===
          // Delete selected vertex in vertex edit mode
          else if (event.logicalKey == LogicalKeyboardKey.delete &&
              controller.isVertexEditModeActive.value &&
              controller.selectedVertexIndex.value >= 0) {
            // Get the vertices of the selected shape
            final vertices = controller.getSelectedShapeVertices();
            if (vertices != null && vertices.length > 3) {
              // Only allow deletion if more than 3 vertices remain
              // Note: This would need to be implemented in the controller
              // For now, just show a message
              Get.snackbar(
                'Vertex Edit',
                'Vertex deletion via keyboard not yet implemented',
                duration: const Duration(seconds: 2),
              );
            }
          }

          // === CUSTOM SHAPE SHORTCUTS ===
          // Ctrl+Z for undo in custom shape creation
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyZ &&
              !HardwareKeyboard.instance.isShiftPressed &&
              controller.isCreatingCustomShape.value) {
            controller.undoCustomShapeVertex();
          }
          // Ctrl+Y for redo in custom shape creation
          else if ((HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed) &&
              event.logicalKey == LogicalKeyboardKey.keyY &&
              controller.isCreatingCustomShape.value) {
            controller.redoCustomShapeVertex();
          }
        }
      },
      child: Listener(
        // Use a Listener to detect pointer events at a lower level than gestures
        onPointerDown: (PointerDownEvent event) async {
          // --- Toolbar Hit Test ---
          // Get the RenderBox of the toolbar to check if the tap falls within it.
          final toolbarContext = controller.toolbarKey.currentContext;
          if (toolbarContext != null) {
            final toolbarRenderBox =
                toolbarContext.findRenderObject() as RenderBox?;
            if (toolbarRenderBox != null) {
              final toolbarPosition =
                  toolbarRenderBox.localToGlobal(Offset.zero);
              final toolbarSize = toolbarRenderBox.size;
              final toolbarRect = Rect.fromLTWH(
                toolbarPosition.dx,
                toolbarPosition.dy,
                toolbarSize.width,
                toolbarSize.height,
              );

              // If the tap position is inside the toolbar's bounds, ignore the tap.
              if (toolbarRect.contains(event.position)) {
                return; // Don't process taps on the toolbar
              }
            }
          }
          // --- End Toolbar Hit Test ---

          // --- Context Menu Hit Test ---
          if (controller.isContextMenuVisible.value) {
            final menuContext = controller.contextMenuKey.currentContext;
            if (menuContext != null) {
              final menuRenderBox =
                  menuContext.findRenderObject() as RenderBox?;
              if (menuRenderBox != null) {
                final menuPosition = menuRenderBox.localToGlobal(Offset.zero);
                final menuSize = menuRenderBox.size;
                final menuRect = Rect.fromLTWH(
                  menuPosition.dx,
                  menuPosition.dy,
                  menuSize.width,
                  menuSize.height,
                );

                // If the tap position is inside the context menu bounds, ignore the tap
                if (menuRect.contains(event.position)) {
                  return; // Don't process taps on the context menu
                }
              }
            }
          }
          // --- End Context Menu Hit Test ---

          // --- CustomShapeCreator Hit Test ---
          // Only check if we're in custom shape creation mode
          if (controller.isCreatingCustomShape.value) {
            final creatorContext =
                controller.customShapeCreatorKey.currentContext;
            if (creatorContext != null) {
              final creatorRenderBox =
                  creatorContext.findRenderObject() as RenderBox?;
              if (creatorRenderBox != null) {
                final creatorPosition =
                    creatorRenderBox.localToGlobal(Offset.zero);
                final creatorSize = creatorRenderBox.size;
                final creatorRect = Rect.fromLTWH(
                  creatorPosition.dx,
                  creatorPosition.dy,
                  creatorSize.width,
                  creatorSize.height,
                );

                // If the tap position is inside the creator's bounds, ignore the tap
                if (creatorRect.contains(event.position)) {
                  return; // Don't process taps on the CustomShapeCreator
                }
              }
            }
          }
          // --- End CustomShapeCreator Hit Test ---

          // --- MultiSelectPanel Hit Test ---
          // Only check if we're in multi-selection mode
          if (controller.isMultiSelectionMode) {
            final panelContext = controller.multiSelectPanelKey.currentContext;
            if (panelContext != null) {
              final panelRenderBox =
                  panelContext.findRenderObject() as RenderBox?;
              if (panelRenderBox != null) {
                final panelPosition = panelRenderBox.localToGlobal(Offset.zero);
                final panelSize = panelRenderBox.size;
                final panelRect = Rect.fromLTWH(
                  panelPosition.dx,
                  panelPosition.dy,
                  panelSize.width,
                  panelSize.height,
                );

                // If the tap position is inside the panel's bounds, ignore the tap
                if (panelRect.contains(event.position)) {
                  return; // Don't process taps on the multi-select panel
                }
              }
            }
          }
          // --- End MultiSelectPanel Hit Test ---

          // Track the number of active pointers (fingers)
          controller.activePointerCount++;

          // If we have more than one finger down, it's a pinch or other multi-touch gesture
          // Let the InteractiveViewer handle those for zooming
          if (controller.activePointerCount <= 1) {
            // For single-touch events, check for handle interactions
            _handleTapDown(context, event.position);
          }
        },
        onPointerUp: (PointerUpEvent event) {
          // Decrement the pointer count
          if (controller.activePointerCount > 0) {
            controller.activePointerCount--;
          }

          // If this was the end of a drag and we're not zooming
          if (controller.isValidDrag.value &&
              controller.activePointerCount == 0) {
            _handleDragEnd();
          }

          // If this was the end of a handle interaction, make sure we keep selection
          // but still end the interaction properly
          if (controller.isHandleInteraction &&
              controller.activePointerCount == 0) {
            // Handle vertex edit mode drag end
            if (controller.isVertexEditModeActive.value &&
                controller.isDraggingVertex.value) {
              controller.endVertexDrag();
            } else {
              // Finish any pending history tracking for other handle manipulations
              controller.finishHistoryTracking();
            }

            // Ensure snap lines are cleared when handle interaction ends
            controller.activeSnapInfo.value = null;

            // Reset handle interaction flag but don't reset selection
            controller.isHandleInteraction = false;
          }
        },
        onPointerMove: (PointerMoveEvent event) {
          // Only handle movements for single-touch interactions
          // Let multi-touch events pass through to the InteractiveViewer
          if (controller.activePointerCount == 1) {
            _handlePointerMove(context, event);
          }
        },
        onPointerCancel: (PointerCancelEvent event) {
          // Clean up in case of cancelled touch
          if (controller.activePointerCount > 0) {
            controller.activePointerCount--;
          }

          if (controller.isValidDrag.value) {
            _handleDragEnd();
          }

          // Also handle cancellation of handle interactions
          if (controller.isHandleInteraction) {
            // Handle vertex edit mode drag cancellation
            if (controller.isVertexEditModeActive.value &&
                controller.isDraggingVertex.value) {
              controller.endVertexDrag();
            } else {
              // Finish any pending history tracking for other operations
              controller.finishHistoryTracking();
            }

            // Clear snap lines when interaction is canceled
            controller.activeSnapInfo.value = null;

            // Reset handle interaction flag without deselecting
            controller.isHandleInteraction = false;
          }
        },
        // Use translucent behavior to allow events to propagate through
        behavior: HitTestBehavior.translucent,
        child: GestureDetector(
          // Use onLongPressStart to get the exact position
          onLongPressStart: (details) {
            _handleLongPressStart(context, details.globalPosition);
          },
          behavior: HitTestBehavior.translucent,
          child: child,
        ),
      ),
    );
  }

  void _handleTapDown(BuildContext context, Offset globalPosition) {
    // If menu is visible OR we're currently interacting with a handle or panning,
    // let the menu/handle logic manage the tap and do nothing further here.
    if (controller.isContextMenuVisible.value ||
        controller.isHandleInteraction ||
        controller.isPanning) {
      return;
    }

    // Set grid as interacting when user taps
    controller.setGridInteracting(true);

    // --- Priority handle hit testing ---
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;
    final Offset localPosition = renderBox
        .globalToLocal(globalPosition); // Tap relative to GlobalTapHandler

    // Get the InteractiveViewer's transformation matrix
    final Matrix4 matrix = controller.transformationController.value;
    // Calculate the inverse matrix to transform tap position into the child's coordinate system (grid)
    final Matrix4 inverseMatrix = Matrix4.inverted(matrix);
    // Transform the local tap position to grid coordinates
    final Offset tapGridPosition =
        MatrixUtils.transformPoint(inverseMatrix, localPosition);

    // --- Check if we're in custom shape creation mode ---
    if (controller.isCreatingCustomShape.value) {
      // If we're in custom shape creation mode, handle the tap with the new method
      controller.handleCustomShapeCanvasTap(tapGridPosition);

      // If we should close the shape, finish the creation
      if (controller.shouldCloseCustomShape.value) {
        controller.finishCustomShapeCreation();
      }

      return; // Skip normal tap handling
    }
    // --- End custom shape creation check ---

    // --- Check if we're in vertex edit mode ---
    if (controller.isVertexEditModeActive.value) {
      // Check if we hit a vertex handle first
      final hitVertexIndex = controller.getHitVertexIndex(tapGridPosition);

      if (hitVertexIndex != -1) {
        // Hit a vertex, start drag interaction
        controller.isHandleInteraction = true;
        controller.startVertexDrag(hitVertexIndex, tapGridPosition);
        controller.setGridInteracting(true);
        return;
      }
      // If we didn't hit a vertex, let normal handling proceed for shape selection
    }
    // --- End vertex edit mode check ---

    final hitHandle = _priorityHandleHitTest(context, tapGridPosition);

    // If we hit a handle, set the flag and return immediately
    if (hitHandle) {
      controller.isHandleInteraction = true;
      // Start history tracking for the handle interaction (e.g., vertex drag, edge drag)
      // The specific operation name will be determined by the handle type later
      controller.startHistoryTracking("Manipulate Handle");
      controller.setGridInteracting(
          true); // Set interacting on handle interaction start
      return;
    }
    // --- End Priority Handle Hit Testing ---

    // --- Pass both grid and global positions to the controller's handler ---
    // Check modifier keys for multi-selection intent
    final isShiftDown = HardwareKeyboard.instance.isShiftPressed;
    final isMultiSelectionMode = controller.isMultiSelectionMode;

    // Call controller's method - Use named arguments now
    final bool hitShape = controller.handleTapEvent(
      context,
      tapGridPosition,
      globalTapPosition: globalPosition,
      isShiftDown: isShiftDown || isMultiSelectionMode,
      isLongPress: false, // Explicitly false for regular taps
    );

    // Set isValidDrag based *only* on whether the precise hit test hit a shape
    // AND we are not interacting with a handle (already checked)
    // AND the tap wasn't inside the context menu (implicitly handled by handleTapEvent returning early)
    controller.isValidDrag = hitShape;

    // Start history tracking if we're going to drag a selected shape
    // This now correctly triggers only if the tap actually hit a shape that is selectable
    if (hitShape && controller.selectedIndices.isNotEmpty) {
      controller.startHistoryTracking(
          "Move Shape${controller.selectedIndices.length > 1 ? 's' : ''}");
      controller.setGridInteracting(true); // Set interacting on drag start
    }

    // Handle exiting persistent multi-selection mode if necessary
    // (This logic remains the same, but is now triggered correctly based on `hitShape`)
    // Use the public helper method from the controller
    if (isMultiSelectionMode &&
        !hitShape &&
        !isShiftDown &&
        !controller.isTapInsideContextMenu(globalPosition)) {
      controller.isMultiSelectionMode = false;
    }
  }

  // Dedicated method for handle hit testing that runs with priority over shape hit testing
  bool _priorityHandleHitTest(BuildContext context, Offset tapGridPosition) {
    if (controller.selectedIndices.isEmpty) return false;

    // --- Calculate Hitbox Size in Grid Units ---
    // Base screen sizes for hitboxes
    final double baseHandleSize = 18.0;
    final double baseVertexSize = 22.0;

    // Scaled screen sizes (visual handle size adjusted for zoom)
    final double scaledScreenHandleSize = _calculateScaledSize(
        baseHandleSize, controller.zoomScale.value, minHandleHitboxSize);
    final double scaledScreenVertexSize = _calculateScaledSize(
        baseVertexSize, controller.zoomScale.value, minVertexHandleHitboxSize);

    // Convert screen hitbox size to grid hitbox size
    // Divide by zoomScale because 1 unit in grid space corresponds to zoomScale units on screen
    final double gridHandleWidth =
        scaledScreenHandleSize / controller.zoomScale.value;
    final double gridVertexWidth =
        scaledScreenVertexSize / controller.zoomScale.value;
    // --- End Hitbox Size Calculation ---

    for (final index in controller.selectedIndices) {
      if (index >= 0 && index < controller.shapes.length) {
        final selectedShape = controller.shapes[index];
        final shapeKey = selectedShape.key;

        if (shapeKey != null) {
          final shapeData = controller.getShapeState(shapeKey);
          if (shapeData != null && shapeData.vertices.isNotEmpty) {
            // Create the transformation matrix for the shape
            final Matrix4 fullTransformMatrix = Matrix4.identity()
              ..translate(shapeData.center.dx, shapeData.center.dy)
              ..rotateZ(shapeData.rotation)
              ..translate(-shapeData.center.dx, -shapeData.center.dy);

            // Get the bounding box corners for vertex handles
            final rect = shapeData.boundingRect;

            // Define the corners of the bounding box
            final boundingBoxCorners = [
              Offset(rect.left, rect.top), // Top-left
              Offset(rect.right, rect.top), // Top-right
              Offset(rect.right, rect.bottom), // Bottom-right
              Offset(rect.left, rect.bottom) // Bottom-left
            ];

            // Apply the shape's transformation to the corners
            final transformedCorners = boundingBoxCorners
                .map((corner) =>
                    MatrixUtils.transformPoint(fullTransformMatrix, corner))
                .toList();

            // Check Vertex Handles using bounding box corners
            for (int i = 0; i < transformedCorners.length; i++) {
              final gridCornerCenter =
                  transformedCorners[i]; // Center in grid coordinates

              // Define hitbox directly in grid coordinates
              final vertexHitbox = Rect.fromCenter(
                center: gridCornerCenter, // Use grid coordinates
                width: gridVertexWidth, // Use grid size
                height:
                    gridVertexWidth, // Use grid size (assuming square hitbox for simplicity)
              );

              // Check against the tap position in grid coordinates
              if (vertexHitbox.contains(tapGridPosition)) {
                return true; // Found hit on vertex handle
              }
            }

            // Check Edge/Curve Handles
            for (int i = 0; i < shapeData.vertices.length; i++) {
              final nextIndex = (i + 1) % shapeData.vertices.length;
              final v1 = shapeData.vertices[i];
              final v2 = shapeData.vertices[nextIndex];
              final controlOffset = shapeData.curveControls[i] ?? Offset.zero;

              Offset handleCenterUntransformed;
              if (controlOffset != Offset.zero) {
                // Calculate curve handle position
                final edgeMidpoint =
                    Offset((v1.dx + v2.dx) / 2, (v1.dy + v2.dy) / 2);
                final controlPoint = edgeMidpoint + controlOffset;
                final edgeVector = v2 - v1;
                final control1 = v1 + edgeVector * 0.25;
                final control2 = v2 - edgeVector * 0.25;
                final adjustedControl1 =
                    control1 + (controlPoint - edgeMidpoint) * 0.5;
                final adjustedControl2 =
                    control2 + (controlPoint - edgeMidpoint) * 0.5;
                handleCenterUntransformed = GeometryUtils.calculateBezierPoint(
                    v1, adjustedControl1, adjustedControl2, v2, 0.5);
              } else {
                // Calculate edge handle position (midpoint of straight edge)
                handleCenterUntransformed =
                    Offset((v1.dx + v2.dx) / 2, (v1.dy + v2.dy) / 2);
              }

              // Calculate handle center in grid coordinates
              final gridHandleCenter = MatrixUtils.transformPoint(
                  fullTransformMatrix, handleCenterUntransformed);

              final edgeHitbox = Rect.fromCenter(
                center: gridHandleCenter, // Use grid coordinates
                width: gridHandleWidth, // Use grid size
                height: gridHandleWidth, // Use grid size
              );

              // Check against the tap position in grid coordinates
              if (edgeHitbox.contains(tapGridPosition)) {
                return true; // Found hit on edge handle
              }
            }
          }
        }
      }
    }

    return false; // No handle hit detected
  }

  void _handlePointerMove(BuildContext context, PointerMoveEvent event) {
    // Handle handle interaction separately first
    if (controller.isHandleInteraction) {
      controller
          .setGridInteracting(true); // Ensure interaction during handle move

      // Handle vertex edit mode dragging
      if (controller.isVertexEditModeActive.value &&
          controller.isDraggingVertex.value &&
          controller.selectedVertexIndex.value >= 0) {
        // Transform the position based on current zoom/pan
        final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          final Offset localPosition = renderBox.globalToLocal(event.position);
          final Matrix4 matrix = controller.transformationController.value;
          final Matrix4 inverseMatrix = Matrix4.inverted(matrix);
          final Offset gridPosition =
              MatrixUtils.transformPoint(inverseMatrix, localPosition);

          // Update vertex position
          controller.updateVertexDragPosition(
              controller.selectedVertexIndex.value, gridPosition);
        }
        return;
      }

      // TODO: Implement other handle drag logic here or call a dedicated controller method
      // For now, just prevent shape dragging during handle interaction
      return;
    }

    // If panning or drag didn't start on a shape, ignore
    if (controller.isPanning || !controller.isValidDrag.value) {
      return;
    }

    // Set grid as interacting when user drags a shape
    controller.setGridInteracting(true);

    // Only handle drag if a shape is selected AND the drag started on a selected shape
    if (controller.selectedIndices.isNotEmpty) {
      // Transform the position based on current zoom scale
      final Offset adjustedPosition = _transformPointForZoom(event.position);
      final delta = event.delta / controller.zoomScale.value;

      // Create drag update details from pointer move event
      final DragUpdateDetails adjustedDetails = DragUpdateDetails(
        sourceTimeStamp: event.timeStamp,
        delta: delta, // Scale the delta by the zoom factor
        primaryDelta: null, // Set to null to avoid assertion errors
        globalPosition: event.position, // Keep original global position
        localPosition: event.localPosition,
      );

      // Use the controller's method to handle dragging with adjusted details
      controller.handleDragUpdate(context, adjustedPosition, adjustedDetails);
    }
  }

  void _handleDragEnd() {
    // Finish history tracking if we were dragging a selected shape
    if (controller.isValidDrag.value) {
      // Call onDragEnd instead of finishHistoryTracking to ensure snap lines are cleared
      controller.onDragEnd();
    }

    // Reset the drag flag when drag ends
    controller.isValidDrag = false;

    // Also reset handle interaction flag if it was set
    controller.isHandleInteraction = false;

    // --- Added: Reset grid interacting state on drag end ---
    controller.setGridInteracting(false);
  }

  // Helper method to transform a point based on the current zoom settings
  Offset _transformPointForZoom(Offset point) {
    // Get the current screen size
    final Size screenSize = MediaQuery.of(Get.context!).size;

    // Use the GridSystem's more accurate screen-to-grid transformation
    // This properly accounts for both panning and zooming, including below screen positions
    return controller.gridSystem.screenToGridPoint(point, screenSize);
  }

  // Use onLongPressStart to get the precise position
  void _handleLongPressStart(BuildContext context, Offset globalPosition) {
    // --- FIX: Reset handle interaction flag for long press start ---
    // This prevents the flag set during initial onPointerDown from blocking the menu
    controller.isHandleInteraction = false;
    // -----------------------------------------------------------

    // If already interacting with a handle or panning, don't do anything
    if (controller.isHandleInteraction || controller.isPanning) {
      return;
    }

    // --- MultiSelectPanel Hit Test for Long Press ---
    // Only check if we're in multi-selection mode
    if (controller.isMultiSelectionMode) {
      final panelContext = controller.multiSelectPanelKey.currentContext;
      if (panelContext != null) {
        final panelRenderBox = panelContext.findRenderObject() as RenderBox?;
        if (panelRenderBox != null) {
          final panelPosition = panelRenderBox.localToGlobal(Offset.zero);
          final panelSize = panelRenderBox.size;
          final panelRect = Rect.fromLTWH(
            panelPosition.dx,
            panelPosition.dy,
            panelSize.width,
            panelSize.height,
          );

          // If the long press position is inside the panel's bounds, ignore it
          if (panelRect.contains(globalPosition)) {
            return; // Don't process long press on the multi-select panel
          }
        }
      }
    }
    // --- End MultiSelectPanel Hit Test ---

    // Convert global position to grid position for hit testing
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;
    final Offset localPosition = renderBox.globalToLocal(globalPosition);
    final Matrix4 matrix = controller.transformationController.value;
    final Matrix4 inverseMatrix = Matrix4.inverted(matrix);
    final Offset tapGridPosition =
        MatrixUtils.transformPoint(inverseMatrix, localPosition);

    // --- Call new controller method to handle selection logic FIRST ---
    controller.handleCanvasLongPress(context, tapGridPosition);

    // --- ALWAYS show context menu after handling long press ---
    controller.showContextMenu(globalPosition);

    /* --- Remove old logic that called handleTapEvent ---
    // Let the controller handle the long press event - Use named args
    controller.handleTapEvent(
      context,
      tapGridPosition,
      globalTapPosition: globalPosition,
      isLongPress: true,
      isShiftDown: false, // Shift doesn't apply to long press
    );
    */
  }

  /// Handle arrow key nudging of selected shapes
  void _handleArrowKeyNudge(LogicalKeyboardKey arrowKey) {
    if (controller.selectedIndices.isEmpty) return;

    // Determine nudge amount based on whether Shift is held
    final bool isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
    final double nudgeAmount =
        isShiftPressed ? 10.0 : 1.0; // Larger nudge with Shift

    // Determine direction based on arrow key
    Offset nudgeDirection;
    switch (arrowKey) {
      case LogicalKeyboardKey.arrowUp:
        nudgeDirection = Offset(0, -nudgeAmount);
        break;
      case LogicalKeyboardKey.arrowDown:
        nudgeDirection = Offset(0, nudgeAmount);
        break;
      case LogicalKeyboardKey.arrowLeft:
        nudgeDirection = Offset(-nudgeAmount, 0);
        break;
      case LogicalKeyboardKey.arrowRight:
        nudgeDirection = Offset(nudgeAmount, 0);
        break;
      default:
        return; // Unknown arrow key
    }

    // Start history tracking for the nudge operation
    controller.startHistoryTracking(
        "Nudge Shape${controller.selectedIndices.length > 1 ? 's' : ''}");

    // Apply nudge to all selected shapes
    for (final index in controller.selectedIndices) {
      if (index >= 0 && index < controller.shapes.length) {
        final shape = controller.shapes[index];
        final shapeKey = shape.key;
        if (shapeKey == null) continue;

        final currentData = controller.getShapeState(shapeKey);
        if (currentData == null) continue;

        // Apply the nudge by translating all vertices and center
        final nudgedVertices = currentData.vertices
            .map((vertex) => vertex + nudgeDirection)
            .toList();
        final nudgedCenter = currentData.center + nudgeDirection;
        final nudgedBoundingRect =
            currentData.boundingRect.shift(nudgeDirection);

        // Create updated shape data
        final updatedData = currentData.copyWith(
          vertices: nudgedVertices,
          center: nudgedCenter,
          boundingRect: nudgedBoundingRect,
        );

        // Save the updated state
        controller.saveShapeState(shapeKey, updatedData);

        // Update the visual representation
        final newShape = TransformableShape(
          key: shapeKey,
          constraints: shape.constraints,
          initialShapeType: shape.initialShapeType,
          initialRect: shape.initialRect,
          selected: true,
          initialShapeData: updatedData,
          initialCurveMode: controller.isCurveModeActive() &&
              controller.selectedIndices.first == index,
        );

        controller.shapes[index] = newShape;
      }
    }

    // Finish history tracking
    controller.finishHistoryTracking();

    // Update the UI
    controller.update();

    // Provide haptic feedback for the nudge
    HapticFeedback.lightImpact();
  }
}
